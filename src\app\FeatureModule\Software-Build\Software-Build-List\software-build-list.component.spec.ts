
import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { UploadPackageResponse } from '../../../model/upload.package.response';
import { DownloadJsonResponse, SubTitleInformation, VideoInformation } from '../../../model/video/download-json-response.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { PermissionService } from '../../../shared/permission.service';
import { HideShowEditIconForSoftwareBuildePipe } from '../../../shared/pipes/HideShowEditIconForSoftwareBuildePipe.pipe';
import { SoftwareBuildMappedDevicePipe } from '../../../shared/pipes/Software Build/software-build-mapped-device.pipe';
import { SoftwareBuildStatusPipe } from '../../../shared/pipes/Software Build/software-build-status.pipe';
import { JsonNamePipe } from '../../../shared/pipes/json-name.pipe';
import { ModelDisplayNameListToStringConvert } from '../../../shared/pipes/ModelDisplayNameListToStringConvert.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { UploadScanService } from '../../../shared/upload-scan.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { VideoService } from '../../../shared/videoservice/video.service';
import { commonsProviders, countryListResponse, testDropdownInteraction, testToggleFilter } from '../../../Tesing-Helper/test-utils';
import { EditSoftwareBuildComponent } from '../Edit-Software-Build/edit-software-build.component';
import { SoftwareBuildConfirmComponent } from '../Software-build-confirm/software-build-confirm.component';
import { SoftwareBuildListComponent } from './software-build-list.component';
import { UploadSoftwareBuildDialogComponent } from '../upload-software-build-dialog/upload-software-build-dialog.component';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { SoftwareBuildFilterComponent } from '../software-build-filter/software-build-filter.component';

describe('ItemInventoryComponent', () => {
  let component: SoftwareBuildListComponent;
  let fixture: ComponentFixture<SoftwareBuildListComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let softwareBuildApiCallServiceMock: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let videoServiceSpy: VideoService;
  let moduleValidationServiceService: ModuleValidationServiceService;
  let downloadService: jasmine.SpyObj<DownloadService>;

  let inventoryListResponse = {
    "content": [{
      "id": 78,
      "version": "21.22.11",
      "title": "rajT201",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566757199,
      "isActive": true,
      "countries": ["Argentina"],
      "deviceTypes": []
    }, {
      "id": 77,
      "version": "21.22.11",
      "title": "rajT20",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566495667,
      "isActive": true,
      "countries": ["Akshay443"],
      "deviceTypes": []
    }, {
      "id": 76,
      "version": "20.22.11",
      "title": "test",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566194841,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 73,
      "version": "8.0.1",
      "title": "80170",
      "attachmentName": "thor-ota-********.zip",
      "releaseNoteName": "2.3.json",
      "jsonMaster": {
        "id": 1,
        "version": "2.3"
      },
      "partNumber": "EN",
      "createdDate": 1733485708685,
      "isActive": true,
      "countries": ["India"],
      "deviceTypes": ["CLIENT"]
    }, {
      "id": 71,
      "version": "7.2.5",
      "title": "DemoTestin121",
      "attachmentName": "Final 1 (2) (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733382706002,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 70,
      "version": "1.2.3",
      "title": "Demo1q32",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P006245-009",
      "createdDate": 1733381445732,
      "isActive": true,
      "countries": ["Bosnia and Herzegovina"],
      "deviceTypes": []
    }, {
      "id": 69,
      "version": "7.2.5",
      "title": "Demo12313",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733380870026,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 68,
      "version": "1.2.3",
      "title": "Testing12emo",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733379217893,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 67,
      "version": "1.2.3",
      "title": "demo1233",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 13,
        "version": "1.2.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733372843029,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 66,
      "version": "1.7.3",
      "title": "AkshayDemo13",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733317463066,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 6,
    "totalElements": 53,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  let videoJosnResponse = [{
    "id": 12,
    "version": "1.1.1"
  }, {
    "id": 13,
    "version": "1.2.1"
  }, {
    "id": 15,
    "version": "1.2.1"
  }, {
    "id": 11,
    "version": "2.1.1"
  }, {
    "id": 19,
    "version": "2.1.3"
  }, {
    "id": 1,
    "version": "2.3"
  }, {
    "id": 21,
    "version": "20.20.20"
  }, {
    "id": 20,
    "version": "test"
  }, {
    "id": 16,
    "version": "Video3_v2-3"
  }]

  let jsonVersion: DownloadJsonResponse = new DownloadJsonResponse(
    "2.3.4",
    [new VideoInformation(
      "sajkl",
      "0:00",
      1234567,
      [new SubTitleInformation("enxsd", "ddccsr")]
    )]
  );
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getSoftwearBuildPermission']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache', 'filterOutUserAssociatedCountries']);
    softwareBuildApiCallServiceMock = jasmine.createSpyObj('InventoryService', ['inventoryList', 'mapInventoryWithDeviceType', 'markInventoriesActiveInactive', 'getAttachmentUrl', 'deleteSoftwearBuild', 'updateInventory', 'pushFileToStorage', 'uploadFileToStorage']);
    downloadService = jasmine.createSpyObj('DownloadService', ['downloadMyFile', 'getisLoadingSubject', 'setLoading']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue(['Algeria', 'Argentina', 'Australia', 'Austria', 'Belgium', 'india']);
    await TestBed.configureTestingModule({

      declarations: [SoftwareBuildListComponent, SoftwareBuildFilterComponent, JsonNamePipe, ModelDisplayNameListToStringConvert, SoftwareBuildMappedDevicePipe, SoftwareBuildStatusPipe, HideShowEditIconForSoftwareBuildePipe, SoftwareBuildConfirmComponent, EditSoftwareBuildComponent, UploadSoftwareBuildDialogComponent],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [{ provide: LocalStorageService, useValue: localStorageServiceMock },
        DatePipe,
        PrintListPipe,
        UploadScanService,
        ExceptionHandlingService,
        SessionStorageService,
        AuthJwtService,
        CommonsService,
        ConfirmDialogService,
        SSOLoginService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
      { provide: DownloadService, useValue: downloadService },
      { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceMock },
      { provide: CountryCacheService, useValue: countryCacheServiceSpy },
      commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildListComponent);
    component = fixture.componentInstance;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    videoServiceSpy = TestBed.inject(VideoService);
    moduleValidationServiceService = TestBed.inject(ModuleValidationServiceService)
    fixture.detectChanges();

    // Mock API call to fetch the Role list and return a successful response
    softwareBuildApiCallServiceMock.inventoryList?.and.returnValue(of(new HttpResponse<any>({
      body: inventoryListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });


});