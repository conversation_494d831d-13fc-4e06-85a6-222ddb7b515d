import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SoftwareBuildModuleComponent } from './software-build-module.component';
import { SoftwareBuildListComponent } from '../Software-Build-List/software-build-list.component';
import { SoftwareBuildFilterComponent } from '../software-build-filter/software-build-filter.component';
import { UploadScanService } from 'src/app/shared/upload-scan.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { DatePipe, CommonModule } from '@angular/common';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

describe('SoftwareBuildModuleComponent', () => {
  let component: SoftwareBuildModuleComponent;
  let fixture: ComponentFixture<SoftwareBuildModuleComponent>;

  let localStorageSpy: jasmine.SpyObj<LocalStorageService>;
  let sessionStorageSpy: jasmine.SpyObj<SessionStorageService>;

  beforeEach(async () => {
    localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['retrieve', 'store', 'clear']);
    sessionStorageSpy = jasmine.createSpyObj('SessionStorageService', ['retrieve', 'store', 'clear']);

    // Optional: set return values for specific calls
    localStorageSpy.retrieve.and.returnValue(null);
    sessionStorageSpy.retrieve.and.returnValue(null);

    await TestBed.configureTestingModule({
      declarations: [
        SoftwareBuildModuleComponent,
        SoftwareBuildListComponent,
        SoftwareBuildFilterComponent,
      ],
      imports: [CommonModule, NgbPaginationModule, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        UploadScanService,
        PermissionService,
        AuthJwtService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: SessionStorageService, useValue: sessionStorageSpy },
        commonsProviders(null)
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
